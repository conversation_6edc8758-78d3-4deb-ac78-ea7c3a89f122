import { Box } from '@/components/Box';
import { cn } from '@/lib/utils';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { IAdsAccount } from '@/types/tiktok';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { RiNewsLine } from '@remixicon/react';
import { NoData } from '@/components/NoData';

type TListBusinessProps = {
  setAccount: (account: IAdsAccount) => void;
};

export const ListBusiness = ({ ...props }: TListBusinessProps) => {
  const { setAccount } = props;
  const { listPages, adsAccount } = useTiktokContext();

  return (
    <Box className="h-[264px]">
      {listPages.length ? (
        <Box
          variant={'col-start'}
          className="flex-1 gap-2 rounded-2xl p-3 h-full border border-big360Color-neutral-300"
        >
          <div className="overflow-auto w-full h-full">
            <RadioGroup
              value={adsAccount?.ad_account_id || ''}
              onValueChange={(value) => {
                const selectedAccount = listPages.find(account => account.ad_account_id === value);
                if (selectedAccount) {
                  setAccount(selectedAccount);
                }
              }}
            >
              {listPages.map((account) => {
                const { ad_account_id, ad_account_name } = account;
                const isChecked = adsAccount?.ad_account_id === ad_account_id;
                return (
                  <Box
                    key={ad_account_id}
                    className={cn(
                      'w-full p-2 gap-1 justify-start rounded-xl hover:bg-big360Color-neutral-50',
                      isChecked && 'bg-big360Color-neutral-50',
                    )}
                  >
                    <div className="flex items-center w-fit space-x-2">
                      <RadioGroupItem
                        value={ad_account_id}
                        id={`account-${ad_account_id}`}
                        className={'border-brand'}
                      />
                    </div>
                    <div
                      className={cn(
                        'p-2 border rounded-full bg-big360Color-neutral-100 mr-1',
                        isChecked
                          ? 'border-big360Color-neutral-00'
                          : 'border-big360Color-neutral-100',
                      )}
                    >
                      <RiNewsLine size={16} color={'#4E5255'} />
                    </div>
                    <p className="flex flex-col text-left">
                      <span className="text-sm font-semibold text-primary-crm">
                        {ad_account_name}
                      </span>
                      <span className="text-xs font-normal text-secondary">ID: {ad_account_id}</span>
                    </p>
                  </Box>
                );
              })}
            </RadioGroup>
          </div>
        </Box>
      ) : (
        <Box variant={'col-center'} className="flex-1 justify-center items-center">
          <NoData className="h-96" />
        </Box>
      )}
    </Box>
  );
};
